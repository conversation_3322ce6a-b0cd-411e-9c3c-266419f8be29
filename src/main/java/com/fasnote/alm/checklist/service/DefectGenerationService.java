package com.fasnote.alm.checklist.service;

import org.springframework.stereotype.Service;

import com.fasnote.alm.checklist.model.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 缺陷生成服务
 */
@Service
public class DefectGenerationService {
    
    /**
     * 根据评审完成时的数据生成缺陷
     *
     * @param review 评审实例
     * @param defectRules 缺陷规则列表
     * @return 生成的缺陷列表
     */
    public List<GeneratedDefect> generateDefectsOnReviewCompletion(ChecklistReview review, List<DefectRule> defectRules) {
        List<GeneratedDefect> generatedDefects = new ArrayList<>();
        
        if (defectRules == null || defectRules.isEmpty()) {
            return generatedDefects;
        }
        
        // 遍历所有评审项
        if (review.getReviewItems() != null) {
            for (ReviewItem item : review.getReviewItems()) {
                // 为每个评审项检查所有缺陷规则
                for (DefectRule rule : defectRules) {
                    if (!rule.isEnabled()) {
                        continue;
                    }
                    
                    // 检查是否满足触发条件
                    if (shouldTriggerDefectGeneration(item, rule)) {
                        GeneratedDefect defect = generateDefect(item, rule, review);
                        if (defect != null) {
                            generatedDefects.add(defect);
                        }
                    }
                }
            }
        }
        
        return generatedDefects;
    }
    
    /**
     * 检查是否应该触发缺陷生成
     *
     * @param item 评审项
     * @param rule 缺陷规则
     * @return 是否触发
     */
    private boolean shouldTriggerDefectGeneration(ReviewItem item, DefectRule rule) {
        DefectRule.DefectTrigger trigger = rule.getTrigger();
        if (trigger == null) {
            return false;
        }
        
        // 检查状态条件
        if (trigger.getStatus() != null && !trigger.getStatus().isEmpty()) {
            if (!trigger.getStatus().contains(item.getStatus().getCode())) {
                return false;
            }
        }
        
        // 检查额外条件
        if (trigger.getConditions() != null && !trigger.getConditions().isEmpty()) {
            return checkConditions(item, trigger.getConditions());
        }
        
        return true;
    }
    
    /**
     * 检查条件是否满足
     *
     * @param item 评审项
     * @param conditions 条件列表
     * @return 是否满足
     */
    private boolean checkConditions(ReviewItem item, List<DefectCondition> conditions) {
        if (conditions == null || conditions.isEmpty()) {
            return true;
        }
        
        boolean result = true;
        String currentLogicOperator = "AND"; // 默认为AND
        
        for (DefectCondition condition : conditions) {
            boolean conditionResult = checkSingleCondition(item, condition);
            
            if ("OR".equals(currentLogicOperator)) {
                result = result || conditionResult;
            } else { // AND
                result = result && conditionResult;
            }
            
            // 更新下一个条件的逻辑操作符
            if (condition.getLogicOperator() != null) {
                currentLogicOperator = condition.getLogicOperator();
            }
        }
        
        return result;
    }
    
    /**
     * 检查单个条件
     *
     * @param item 评审项
     * @param condition 条件
     * @return 是否满足
     */
    private boolean checkSingleCondition(ReviewItem item, DefectCondition condition) {
        String field = condition.getField();
        String operator = condition.getOperator();
        Object expectedValue = condition.getValue();
        
        // 获取字段值
        Object actualValue = getFieldValue(item, field);
        
        // 根据操作符进行比较
        switch (operator) {
            case "equals":
                return Objects.equals(actualValue, expectedValue);
            case "contains":
                return actualValue != null && actualValue.toString().contains(expectedValue.toString());
            case "startsWith":
                return actualValue != null && actualValue.toString().startsWith(expectedValue.toString());
            case "endsWith":
                return actualValue != null && actualValue.toString().endsWith(expectedValue.toString());
            case "regex":
                return actualValue != null && Pattern.matches(expectedValue.toString(), actualValue.toString());
            case "exists":
                return actualValue != null;
            case "notExists":
                return actualValue == null;
            default:
                return false;
        }
    }
    
    /**
     * 获取字段值
     *
     * @param item 评审项
     * @param field 字段名
     * @return 字段值
     */
    private Object getFieldValue(ReviewItem item, String field) {
        switch (field) {
            case "content":
                return item.getContent();
            case "category":
                return item.getCategory();
            case "status":
                return item.getStatus();
            case "comment":
                return item.getComment();
            default:
                // 检查自定义字段
                if (item.getCustomFields() != null) {
                    return item.getCustomFields().get(field);
                }
                return null;
        }
    }
    
    /**
     * 生成缺陷
     *
     * @param item 评审项
     * @param rule 缺陷规则
     * @param review 评审实例
     * @return 生成的缺陷
     */
    private GeneratedDefect generateDefect(ReviewItem item, DefectRule rule, ChecklistReview review) {
        DefectRule.DefectTemplate template = rule.getTemplate();
        if (template == null) {
            return null;
        }
        
        // 创建缺陷实例
        GeneratedDefect defect = new GeneratedDefect();
        defect.setId("defect_" + System.currentTimeMillis() + "_" + item.getItemId());
        defect.setSourceItemId(item.getItemId());
        defect.setSourceContent(item.getContent());
        
        // 创建变量上下文
        Map<String, Object> context = createVariableContext(item, review);
        
        // 生成标题和描述
        defect.setTitle(replaceTemplateVariables(template.getTitleTemplate(), context));
        defect.setDescription(replaceTemplateVariables(template.getDescriptionTemplate(), context));
        
        // 确定严重程度
        String severity = determineSeverity(item, template.getSeverityMapping());
        defect.setSeverity(severity);
        
        // 确定分类
        String category = determineCategory(item, template.getCategoryMapping());
        defect.setCategory(category);
        
        // 处理自定义字段映射
        if (template.getCustomFieldMapping() != null) {
            Map<String, Object> customData = new HashMap<>();
            for (Map.Entry<String, String> entry : template.getCustomFieldMapping().entrySet()) {
                String sourceField = entry.getKey();
                String targetField = entry.getValue();
                Object value = getFieldValue(item, sourceField);
                if (value != null) {
                    customData.put(targetField, value);
                }
            }
            defect.setCustomData(customData);
        }
        
        return defect;
    }
    
    /**
     * 创建变量上下文
     */
    private Map<String, Object> createVariableContext(ReviewItem item, ChecklistReview review) {
        Map<String, Object> context = new HashMap<>();
        context.put("itemId", item.getItemId());
        context.put("content", item.getContent());
        context.put("category", item.getCategory());
        context.put("status", item.getStatus());
        context.put("comment", item.getComment());
        context.put("reviewId", review.getId());
        context.put("templateId", review.getTemplateId());
        context.put("currentTime", LocalDateTime.now().toString());
        
        // 添加自定义字段
        if (item.getCustomFields() != null) {
            context.put("customFields", item.getCustomFields());
        }
        
        return context;
    }
    
    /**
     * 替换模板变量
     */
    private String replaceTemplateVariables(String template, Map<String, Object> context) {
        if (template == null) {
            return "";
        }
        
        String result = template;
        for (Map.Entry<String, Object> entry : context.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replace(placeholder, value);
        }
        
        return result;
    }
    
    /**
     * 确定严重程度
     */
    private String determineSeverity(ReviewItem item, Map<String, String> severityMapping) {
        if (severityMapping == null) {
            return "medium";
        }
        
        // 首先尝试根据分类映射
        if (item.getCategory() != null && severityMapping.containsKey(item.getCategory())) {
            return severityMapping.get(item.getCategory());
        }
        
        // 使用默认值
        return severityMapping.getOrDefault("default", "medium");
    }
    
    /**
     * 确定分类
     */
    private String determineCategory(ReviewItem item, Map<String, String> categoryMapping) {
        if (categoryMapping == null) {
            return "general";
        }
        
        // 首先尝试根据分类映射
        if (item.getCategory() != null && categoryMapping.containsKey(item.getCategory())) {
            return categoryMapping.get(item.getCategory());
        }
        
        // 使用默认值
        return categoryMapping.getOrDefault("default", "general");
    }
}
