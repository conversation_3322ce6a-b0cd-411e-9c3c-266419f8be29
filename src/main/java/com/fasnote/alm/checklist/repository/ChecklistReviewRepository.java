package com.fasnote.alm.checklist.repository;

import com.fasnote.alm.checklist.model.ChecklistReview;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 检查单评审仓库实现类
 */
public class ChecklistReviewRepository extends BaseJsonFileRepository<ChecklistReview, String> {
    
    private static final String DATA_DIRECTORY = "data/reviews";
    
    public ChecklistReviewRepository() {
        super(DATA_DIRECTORY, ChecklistReview.class, new TypeReference<List<ChecklistReview>>() {});
    }
    
    /**
     * 测试用构造函数，允许指定数据目录
     * 
     * @param dataDirectory 数据目录
     */
    public ChecklistReviewRepository(String dataDirectory) {
        super(dataDirectory, ChecklistReview.class, new TypeReference<List<ChecklistReview>>() {});
    }
    
    @Override
    protected String getEntityId(ChecklistReview entity) {
        return entity.getId();
    }
    
    @Override
    protected void setEntityId(ChecklistReview entity, String id) {
        entity.setId(id);
    }
    
    @Override
    protected String generateNewId() {
        return "review_" + UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 根据模板ID查找评审
     * 
     * @param templateId 模板ID
     * @return 符合条件的评审列表
     * @throws IOException IO异常
     */
    public List<ChecklistReview> findByTemplateId(String templateId) throws IOException {
        if (templateId == null || templateId.trim().isEmpty()) {
            throw new IllegalArgumentException("模板ID不能为空");
        }
        
        return findBy(review -> templateId.equals(review.getTemplateId()));
    }
    
    /**
     * 根据类型查找评审
     * 
     * @param type 评审类型
     * @return 符合条件的评审列表
     * @throws IOException IO异常
     */
    public List<ChecklistReview> findByType(String type) throws IOException {
        if (type == null || type.trim().isEmpty()) {
            throw new IllegalArgumentException("评审类型不能为空");
        }
        
        return findBy(review -> type.equals(review.getType()));
    }
    
    /**
     * 根据状态查找评审
     * 
     * @param status 评审状态
     * @return 符合条件的评审列表
     * @throws IOException IO异常
     */
    public List<ChecklistReview> findByStatus(String status) throws IOException {
        if (status == null || status.trim().isEmpty()) {
            throw new IllegalArgumentException("评审状态不能为空");
        }
        
        return findBy(review -> status.equals(review.getStatus()));
    }
    
    /**
     * 根据创建时间范围查找评审
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 符合条件的评审列表
     * @throws IOException IO异常
     */
    public List<ChecklistReview> findByCreatedTimeBetween(LocalDateTime startTime, LocalDateTime endTime) throws IOException {
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("时间范围不能为空");
        }
        
        if (startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        
        return findBy(review -> {
            LocalDateTime createdTime = review.getCreatedTime();
            return createdTime != null && 
                   !createdTime.isBefore(startTime) && 
                   !createdTime.isAfter(endTime);
        });
    }
    
    /**
     * 根据模板ID和版本查找评审
     * 
     * @param templateId 模板ID
     * @param templateVersion 模板版本
     * @return 符合条件的评审列表
     * @throws IOException IO异常
     */
    public List<ChecklistReview> findByTemplateIdAndVersion(String templateId, String templateVersion) throws IOException {
        if (templateId == null || templateId.trim().isEmpty() || 
            templateVersion == null || templateVersion.trim().isEmpty()) {
            throw new IllegalArgumentException("模板ID和版本不能为空");
        }
        
        return findBy(review -> 
            templateId.equals(review.getTemplateId()) && 
            templateVersion.equals(review.getTemplateVersion()));
    }
    
    /**
     * 获取最近创建的评审列表
     * 
     * @param limit 限制数量
     * @return 最近创建的评审列表
     * @throws IOException IO异常
     */
    public List<ChecklistReview> findRecentReviews(int limit) throws IOException {
        if (limit <= 0) {
            throw new IllegalArgumentException("限制数量必须大于0");
        }
        
        List<ChecklistReview> allReviews = findAll();
        
        // 按创建时间倒序排序
        allReviews.sort((r1, r2) -> {
            if (r1.getCreatedTime() == null && r2.getCreatedTime() == null) {
                return 0;
            }
            if (r1.getCreatedTime() == null) {
                return 1;
            }
            if (r2.getCreatedTime() == null) {
                return -1;
            }
            return r2.getCreatedTime().compareTo(r1.getCreatedTime());
        });
        
        // 返回指定数量的结果
        return allReviews.subList(0, Math.min(limit, allReviews.size()));
    }
    
    /**
     * 统计指定状态的评审数量
     * 
     * @param status 评审状态
     * @return 评审数量
     * @throws IOException IO异常
     */
    public long countByStatus(String status) throws IOException {
        if (status == null || status.trim().isEmpty()) {
            return 0;
        }
        
        List<ChecklistReview> reviews = findByStatus(status);
        return reviews.size();
    }
    
    /**
     * 统计指定类型的评审数量
     * 
     * @param type 评审类型
     * @return 评审数量
     * @throws IOException IO异常
     */
    public long countByType(String type) throws IOException {
        if (type == null || type.trim().isEmpty()) {
            return 0;
        }
        
        List<ChecklistReview> reviews = findByType(type);
        return reviews.size();
    }
    
    /**
     * 检查指定模板是否有关联的评审
     * 
     * @param templateId 模板ID
     * @return 是否有关联评审
     * @throws IOException IO异常
     */
    public boolean existsByTemplateId(String templateId) throws IOException {
        if (templateId == null || templateId.trim().isEmpty()) {
            return false;
        }
        
        List<ChecklistReview> reviews = findByTemplateId(templateId);
        return !reviews.isEmpty();
    }
}