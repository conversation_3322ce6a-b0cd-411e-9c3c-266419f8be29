package com.fasnote.alm.checklist.controller;

import com.fasnote.alm.checklist.model.ReviewTemplateVersion;
import com.fasnote.alm.checklist.model.TemplateSnapshot;
import com.fasnote.alm.checklist.model.VersionComparison;
import com.fasnote.alm.checklist.service.TemplateVersionService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模板版本管理控制器
 */
@RestController
@RequestMapping("/template-version")
@CrossOrigin(origins = "*")
public class TemplateVersionController {
    
    @Autowired
    private TemplateVersionService templateVersionService;
    
    /**
     * 创建模板快照
     */
    @PostMapping("/snapshots")
    public ResponseEntity<Map<String, Object>> createSnapshot(@RequestBody Map<String, Object> request) {
        try {
            String templateId = (String) request.get("templateId");
            String version = (String) request.get("version");
            String description = (String) request.get("description");
            
            if (templateId == null || templateId.trim().isEmpty()) {
                return createErrorResponse("模板ID不能为空", HttpStatus.BAD_REQUEST);
            }
            if (version == null || version.trim().isEmpty()) {
                return createErrorResponse("版本号不能为空", HttpStatus.BAD_REQUEST);
            }
            
            TemplateSnapshot snapshot = templateVersionService.createSnapshot(templateId, version, description);
            return createSuccessResponse(snapshot);
            
        } catch (Exception e) {
            return createErrorResponse("创建模板快照失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 获取模板的所有快照
     */
    @GetMapping("/templates/{templateId}/snapshots")
    public ResponseEntity<Map<String, Object>> getTemplateSnapshots(
            @PathVariable String templateId,
            @RequestParam(defaultValue = "20") int limit,
            @RequestParam(defaultValue = "0") int offset,
            @RequestParam(defaultValue = "false") boolean includeContent) {
        try {
            Map<String, Object> result = templateVersionService.getTemplateSnapshots(
                templateId, limit, offset, includeContent);
            return createSuccessResponse(result);
            
        } catch (Exception e) {
            return createErrorResponse("获取模板快照失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 获取特定快照详情
     */
    @GetMapping("/snapshots/{snapshotId}")
    public ResponseEntity<Map<String, Object>> getSnapshot(@PathVariable String snapshotId) {
        try {
            TemplateSnapshot snapshot = templateVersionService.getSnapshot(snapshotId);
            if (snapshot == null) {
                return createErrorResponse("快照不存在", HttpStatus.NOT_FOUND);
            }
            return createSuccessResponse(snapshot);
            
        } catch (Exception e) {
            return createErrorResponse("获取快照详情失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 删除快照
     */
    @DeleteMapping("/snapshots/{snapshotId}")
    public ResponseEntity<Map<String, Object>> deleteSnapshot(@PathVariable String snapshotId) {
        try {
            boolean success = templateVersionService.deleteSnapshot(snapshotId);
            if (!success) {
                return createErrorResponse("快照不存在", HttpStatus.NOT_FOUND);
            }
            return createSuccessResponse("快照删除成功");
            
        } catch (Exception e) {
            return createErrorResponse("删除快照失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 为检查单创建模板版本关联
     */
    @PostMapping("/review-versions")
    public ResponseEntity<Map<String, Object>> createReviewTemplateVersion(@RequestBody Map<String, Object> request) {
        try {
            String reviewId = (String) request.get("reviewId");
            String templateId = (String) request.get("templateId");
            String snapshotId = (String) request.get("snapshotId");
            
            if (reviewId == null || reviewId.trim().isEmpty()) {
                return createErrorResponse("检查单ID不能为空", HttpStatus.BAD_REQUEST);
            }
            if (templateId == null || templateId.trim().isEmpty()) {
                return createErrorResponse("模板ID不能为空", HttpStatus.BAD_REQUEST);
            }
            
            ReviewTemplateVersion reviewVersion = templateVersionService.createReviewTemplateVersion(
                reviewId, templateId, snapshotId);
            return createSuccessResponse(reviewVersion);
            
        } catch (Exception e) {
            return createErrorResponse("创建检查单模板版本关联失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 获取检查单的模板版本信息
     */
    @GetMapping("/reviews/{reviewId}/template-version")
    public ResponseEntity<Map<String, Object>> getReviewTemplateVersion(@PathVariable String reviewId) {
        try {
            ReviewTemplateVersion reviewVersion = templateVersionService.getReviewTemplateVersion(reviewId);
            if (reviewVersion == null) {
                return createErrorResponse("检查单模板版本信息不存在", HttpStatus.NOT_FOUND);
            }
            return createSuccessResponse(reviewVersion);
            
        } catch (Exception e) {
            return createErrorResponse("获取检查单模板版本信息失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 比较两个版本
     */
    @PostMapping("/compare")
    public ResponseEntity<Map<String, Object>> compareVersions(@RequestBody Map<String, Object> request) {
        try {
            String currentSnapshotId = (String) request.get("currentSnapshotId");
            String targetSnapshotId = (String) request.get("targetSnapshotId");
            
            if (currentSnapshotId == null || currentSnapshotId.trim().isEmpty() ||
                targetSnapshotId == null || targetSnapshotId.trim().isEmpty()) {
                return createErrorResponse("快照ID不能为空", HttpStatus.BAD_REQUEST);
            }
            
            VersionComparison comparison = templateVersionService.compareVersions(currentSnapshotId, targetSnapshotId);
            return createSuccessResponse(comparison);
            
        } catch (Exception e) {
            return createErrorResponse("比较版本失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 检查检查单是否需要版本升级
     */
    @GetMapping("/reviews/{reviewId}/check-upgrade")
    public ResponseEntity<Map<String, Object>> checkUpgradeAvailable(@PathVariable String reviewId) {
        try {
            Map<String, Object> result = templateVersionService.checkUpgradeAvailable(reviewId);
            return createSuccessResponse(result);
            
        } catch (Exception e) {
            return createErrorResponse("检查版本升级失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 升级检查单到新版本
     */
    @PostMapping("/reviews/{reviewId}/upgrade")
    public ResponseEntity<Map<String, Object>> upgradeReviewVersion(
            @PathVariable String reviewId, 
            @RequestBody Map<String, Object> options) {
        try {
            Map<String, Object> result = templateVersionService.upgradeReviewVersion(reviewId, options);
            return createSuccessResponse(result);
            
        } catch (Exception e) {
            return createErrorResponse("版本升级失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 回滚检查单到指定版本
     */
    @PostMapping("/reviews/{reviewId}/rollback")
    public ResponseEntity<Map<String, Object>> rollbackReviewVersion(
            @PathVariable String reviewId, 
            @RequestBody Map<String, Object> request) {
        try {
            String targetSnapshotId = (String) request.get("targetSnapshotId");
            @SuppressWarnings("unchecked")
            Map<String, Object> options = (Map<String, Object>) request.get("options");
            
            if (targetSnapshotId == null || targetSnapshotId.trim().isEmpty()) {
                return createErrorResponse("目标快照ID不能为空", HttpStatus.BAD_REQUEST);
            }
            
            Map<String, Object> result = templateVersionService.rollbackReviewVersion(reviewId, targetSnapshotId, options);
            return createSuccessResponse(result);
            
        } catch (Exception e) {
            return createErrorResponse("版本回滚失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 获取检查单的版本历史
     */
    @GetMapping("/reviews/{reviewId}/history")
    public ResponseEntity<Map<String, Object>> getReviewVersionHistory(@PathVariable String reviewId) {
        try {
            List<Map<String, Object>> history = templateVersionService.getReviewVersionHistory(reviewId);
            return createSuccessResponse(history);
            
        } catch (Exception e) {
            return createErrorResponse("获取版本历史失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 获取模板版本管理配置
     */
    @GetMapping("/templates/{templateId}/config")
    public ResponseEntity<Map<String, Object>> getVersionConfig(@PathVariable String templateId) {
        try {
            Map<String, Object> config = templateVersionService.getVersionConfig(templateId);
            return createSuccessResponse(config);
            
        } catch (Exception e) {
            return createErrorResponse("获取版本配置失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 更新模板版本管理配置
     */
    @PutMapping("/templates/{templateId}/config")
    public ResponseEntity<Map<String, Object>> updateVersionConfig(
            @PathVariable String templateId, 
            @RequestBody Map<String, Object> versionConfig) {
        try {
            templateVersionService.updateVersionConfig(templateId, versionConfig);
            return createSuccessResponse("版本配置更新成功");
            
        } catch (Exception e) {
            return createErrorResponse("更新版本配置失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 自动创建快照（基于配置规则）
     */
    @PostMapping("/templates/{templateId}/auto-snapshot")
    public ResponseEntity<Map<String, Object>> autoCreateSnapshot(
            @PathVariable String templateId, 
            @RequestBody Map<String, Object> request) {
        try {
            String changeType = (String) request.get("changeType");
            TemplateSnapshot snapshot = templateVersionService.autoCreateSnapshot(templateId, changeType);
            return createSuccessResponse(snapshot);
            
        } catch (Exception e) {
            return createErrorResponse("自动创建快照失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    // 辅助方法
    private ResponseEntity<Map<String, Object>> createSuccessResponse(Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", data);
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }
    
    private ResponseEntity<Map<String, Object>> createErrorResponse(String message, HttpStatus status) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.status(status).body(response);
    }
}
