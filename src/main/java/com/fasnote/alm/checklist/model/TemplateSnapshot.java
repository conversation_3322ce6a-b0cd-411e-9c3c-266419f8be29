package com.fasnote.alm.checklist.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import java.time.LocalDateTime;

/**
 * 模板快照
 */
public class TemplateSnapshot {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("templateId")
    private String templateId;
    
    @JsonProperty("version")
    private String version;
    
    @JsonProperty("createdTime")
    private LocalDateTime createdTime;
    
    @JsonProperty("createdBy")
    private String createdBy;
    
    @JsonProperty("description")
    private String description;
    
    // 模板基本信息快照
    @JsonProperty("template")
    private JsonNode templateData;
    
    // 检查项快照
    @JsonProperty("items")
    private JsonNode itemsData;
    
    // 配置快照
    @JsonProperty("config")
    private JsonNode configData;
    
    // 元数据
    @JsonProperty("metadata")
    private SnapshotMetadata metadata;
    
    // 内容哈希
    @JsonProperty("contentHash")
    private String contentHash;
    
    // 构造函数
    public TemplateSnapshot() {
        this.createdTime = LocalDateTime.now();
    }
    
    public TemplateSnapshot(String templateId, String version, String createdBy, 
                           JsonNode templateData, JsonNode itemsData, JsonNode configData) {
        this();
        this.id = "snapshot_" + System.currentTimeMillis();
        this.templateId = templateId;
        this.version = version;
        this.createdBy = createdBy;
        this.templateData = templateData;
        this.itemsData = itemsData;
        this.configData = configData;
        
        // 计算元数据
        this.metadata = calculateMetadata();
        
        // 计算内容哈希
        this.contentHash = calculateContentHash();
    }
    
    private SnapshotMetadata calculateMetadata() {
        SnapshotMetadata meta = new SnapshotMetadata();
        
        if (itemsData != null && itemsData.isArray()) {
            meta.setItemCount(itemsData.size());
            
            int requiredCount = 0;
            for (JsonNode item : itemsData) {
                if (item.has("required") && item.get("required").asBoolean()) {
                    requiredCount++;
                }
            }
            meta.setRequiredItemCount(requiredCount);
        }
        
        if (configData != null) {
            if (configData.has("buttonGroups")) {
                meta.setButtonGroupCount(configData.get("buttonGroups").size());
            }
            if (configData.has("defectRules")) {
                meta.setDefectRuleCount(configData.get("defectRules").size());
            }
        }
        
        return meta;
    }
    
    private String calculateContentHash() {
        // 简单的哈希计算
        StringBuilder content = new StringBuilder();
        if (templateData != null) content.append(templateData.toString());
        if (itemsData != null) content.append(itemsData.toString());
        if (configData != null) content.append(configData.toString());
        
        return String.valueOf(content.toString().hashCode());
    }
    
    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getTemplateId() { return templateId; }
    public void setTemplateId(String templateId) { this.templateId = templateId; }
    
    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }
    
    public LocalDateTime getCreatedTime() { return createdTime; }
    public void setCreatedTime(LocalDateTime createdTime) { this.createdTime = createdTime; }
    
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public JsonNode getTemplateData() { return templateData; }
    public void setTemplateData(JsonNode templateData) { this.templateData = templateData; }
    
    public JsonNode getItemsData() { return itemsData; }
    public void setItemsData(JsonNode itemsData) { this.itemsData = itemsData; }
    
    public JsonNode getConfigData() { return configData; }
    public void setConfigData(JsonNode configData) { this.configData = configData; }
    
    public SnapshotMetadata getMetadata() { return metadata; }
    public void setMetadata(SnapshotMetadata metadata) { this.metadata = metadata; }
    
    public String getContentHash() { return contentHash; }
    public void setContentHash(String contentHash) { this.contentHash = contentHash; }
    
    /**
     * 快照元数据
     */
    public static class SnapshotMetadata {
        @JsonProperty("itemCount")
        private int itemCount;
        
        @JsonProperty("requiredItemCount")
        private int requiredItemCount;
        
        @JsonProperty("categoryCount")
        private int categoryCount;
        
        @JsonProperty("buttonGroupCount")
        private int buttonGroupCount;
        
        @JsonProperty("defectRuleCount")
        private int defectRuleCount;
        
        // Getters and Setters
        public int getItemCount() { return itemCount; }
        public void setItemCount(int itemCount) { this.itemCount = itemCount; }
        
        public int getRequiredItemCount() { return requiredItemCount; }
        public void setRequiredItemCount(int requiredItemCount) { this.requiredItemCount = requiredItemCount; }
        
        public int getCategoryCount() { return categoryCount; }
        public void setCategoryCount(int categoryCount) { this.categoryCount = categoryCount; }
        
        public int getButtonGroupCount() { return buttonGroupCount; }
        public void setButtonGroupCount(int buttonGroupCount) { this.buttonGroupCount = buttonGroupCount; }
        
        public int getDefectRuleCount() { return defectRuleCount; }
        public void setDefectRuleCount(int defectRuleCount) { this.defectRuleCount = defectRuleCount; }
    }
}
