package com.fasnote.alm.checklist.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 评审实例数据模型
 */
public class ChecklistReview {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("templateId")
    private String templateId;
    
    @JsonProperty("templateVersion")
    private String templateVersion;
    
    @JsonProperty("type")
    private String type;
    
    @JsonProperty("createdTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("reviewItems")
    private List<ReviewItem> reviewItems;
    
    // 默认构造函数
    public ChecklistReview() {}
    
    // 全参构造函数
    public ChecklistReview(String id, String templateId, String templateVersion, 
                          String type, LocalDateTime createdTime, String status, 
                          List<ReviewItem> reviewItems) {
        this.id = id;
        this.templateId = templateId;
        this.templateVersion = templateVersion;
        this.type = type;
        this.createdTime = createdTime;
        this.status = status;
        this.reviewItems = reviewItems;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getTemplateId() {
        return templateId;
    }
    
    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }
    
    public String getTemplateVersion() {
        return templateVersion;
    }
    
    public void setTemplateVersion(String templateVersion) {
        this.templateVersion = templateVersion;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public List<ReviewItem> getReviewItems() {
        return reviewItems;
    }
    
    public void setReviewItems(List<ReviewItem> reviewItems) {
        this.reviewItems = reviewItems;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ChecklistReview that = (ChecklistReview) o;
        return Objects.equals(id, that.id) &&
               Objects.equals(templateId, that.templateId) &&
               Objects.equals(templateVersion, that.templateVersion);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, templateId, templateVersion);
    }
    
    @Override
    public String toString() {
        return "ChecklistReview{" +
               "id='" + id + '\'' +
               ", templateId='" + templateId + '\'' +
               ", templateVersion='" + templateVersion + '\'' +
               ", type='" + type + '\'' +
               ", createdTime=" + createdTime +
               ", status='" + status + '\'' +
               ", reviewItems=" + reviewItems +
               '}';
    }
}