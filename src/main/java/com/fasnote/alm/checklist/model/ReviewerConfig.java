package com.fasnote.alm.checklist.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * 评审人配置
 */
public class ReviewerConfig {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("email")
    private String email;
    
    @JsonProperty("role")
    private String role; // "lead", "reviewer", "observer"
    
    @JsonProperty("department")
    private String department;
    
    @JsonProperty("assignedCategories")
    private List<String> assignedCategories;
    
    @JsonProperty("maxAssignedItems")
    private Integer maxAssignedItems;
    
    @JsonProperty("isDefault")
    private Boolean isDefault;
    
    // 默认构造函数
    public ReviewerConfig() {}
    
    // 全参构造函数
    public ReviewerConfig(String id, String name, String email, String role,
                         String department, List<String> assignedCategories,
                         Integer maxAssignedItems, Boolean isDefault) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.role = role;
        this.department = department;
        this.assignedCategories = assignedCategories;
        this.maxAssignedItems = maxAssignedItems;
        this.isDefault = isDefault;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
    
    public List<String> getAssignedCategories() {
        return assignedCategories;
    }
    
    public void setAssignedCategories(List<String> assignedCategories) {
        this.assignedCategories = assignedCategories;
    }
    
    public Integer getMaxAssignedItems() {
        return maxAssignedItems;
    }
    
    public void setMaxAssignedItems(Integer maxAssignedItems) {
        this.maxAssignedItems = maxAssignedItems;
    }
    
    public Boolean getIsDefault() {
        return isDefault;
    }
    
    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }
    
    @Override
    public String toString() {
        return "ReviewerConfig{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", role='" + role + '\'' +
                ", department='" + department + '\'' +
                ", assignedCategories=" + assignedCategories +
                ", maxAssignedItems=" + maxAssignedItems +
                ", isDefault=" + isDefault +
                '}';
    }
}
