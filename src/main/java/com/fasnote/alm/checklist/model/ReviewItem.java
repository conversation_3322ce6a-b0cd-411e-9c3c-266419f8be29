package com.fasnote.alm.checklist.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 评审项数据模型
 */
public class ReviewItem {
    
    @JsonProperty("itemId")
    private String itemId;

    @JsonProperty("sequence")
    private Integer sequence;

    @JsonProperty("content")
    private String content;

    @JsonProperty("category")
    private String category;

    @JsonProperty("required")
    private Boolean required;

    @JsonProperty("status")
    private ReviewStatus status;

    @JsonProperty("comment")
    private String comment;

    @JsonProperty("reviewHistory")
    private List<ReviewRecord> reviewHistory;

    @JsonProperty("customFields")
    private Map<String, Object> customFields;

    // 默认构造函数
    public ReviewItem() {}

    // 简化构造函数（用于测试）
    public ReviewItem(String itemId, ReviewStatus status, String comment, List<ReviewRecord> reviewHistory) {
        this.itemId = itemId;
        this.status = status;
        this.comment = comment;
        this.reviewHistory = reviewHistory;
    }

    // 全参构造函数
    public ReviewItem(String itemId, Integer sequence, String content, String category,
                     Boolean required, ReviewStatus status, String comment,
                     List<ReviewRecord> reviewHistory, Map<String, Object> customFields) {
        this.itemId = itemId;
        this.sequence = sequence;
        this.content = content;
        this.category = category;
        this.required = required;
        this.status = status;
        this.comment = comment;
        this.reviewHistory = reviewHistory;
        this.customFields = customFields;
    }
    
    // Getters and Setters
    public String getItemId() {
        return itemId;
    }
    
    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Boolean getRequired() {
        return required;
    }

    public void setRequired(Boolean required) {
        this.required = required;
    }

    public ReviewStatus getStatus() {
        return status;
    }
    
    public void setStatus(ReviewStatus status) {
        this.status = status;
    }
    
    public String getComment() {
        return comment;
    }
    
    public void setComment(String comment) {
        this.comment = comment;
    }
    
    public List<ReviewRecord> getReviewHistory() {
        return reviewHistory;
    }
    
    public void setReviewHistory(List<ReviewRecord> reviewHistory) {
        this.reviewHistory = reviewHistory;
    }

    public Map<String, Object> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(Map<String, Object> customFields) {
        this.customFields = customFields;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReviewItem that = (ReviewItem) o;
        return Objects.equals(itemId, that.itemId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(itemId);
    }
    
    @Override
    public String toString() {
        return "ReviewItem{" +
               "itemId='" + itemId + '\'' +
               ", status=" + status +
               ", comment='" + comment + '\'' +
               ", reviewHistory=" + reviewHistory +
               '}';
    }
}