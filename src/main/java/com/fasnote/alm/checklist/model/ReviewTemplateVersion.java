package com.fasnote.alm.checklist.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;

/**
 * 检查单模板版本关联
 */
public class ReviewTemplateVersion {
    @JsonProperty("reviewId")
    private String reviewId;
    
    @JsonProperty("templateId")
    private String templateId;
    
    @JsonProperty("snapshotId")
    private String snapshotId;
    
    @JsonProperty("version")
    private String version;
    
    @JsonProperty("createdTime")
    private LocalDateTime createdTime;
    
    @JsonProperty("snapshot")
    private TemplateSnapshot snapshot;
    
    public ReviewTemplateVersion() {
        this.createdTime = LocalDateTime.now();
    }
    
    public ReviewTemplateVersion(String reviewId, String templateId, String snapshotId, String version) {
        this();
        this.reviewId = reviewId;
        this.templateId = templateId;
        this.snapshotId = snapshotId;
        this.version = version;
    }
    
    // Getters and Setters
    public String getReviewId() { return reviewId; }
    public void setReviewId(String reviewId) { this.reviewId = reviewId; }
    
    public String getTemplateId() { return templateId; }
    public void setTemplateId(String templateId) { this.templateId = templateId; }
    
    public String getSnapshotId() { return snapshotId; }
    public void setSnapshotId(String snapshotId) { this.snapshotId = snapshotId; }
    
    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }
    
    public LocalDateTime getCreatedTime() { return createdTime; }
    public void setCreatedTime(LocalDateTime createdTime) { this.createdTime = createdTime; }
    
    public TemplateSnapshot getSnapshot() { return snapshot; }
    public void setSnapshot(TemplateSnapshot snapshot) { this.snapshot = snapshot; }
}
